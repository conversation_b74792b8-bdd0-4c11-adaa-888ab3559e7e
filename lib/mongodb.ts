import { MongoClient, Db, Collection } from 'mongodb'

if (!process.env.MONGODBCONNECTION) {
  throw new Error('Please add your MongoDB connection string to .env.local')
}

// MongoDB connection configured

const uri = process.env.MONGODBCONNECTION
const options = {}

let client: MongoClient
let clientPromise: Promise<MongoClient>

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  let globalWithMongo = global as typeof globalThis & {
    _mongoClientPromise?: Promise<MongoClient>
  }

  if (!globalWithMongo._mongoClientPromise) {
    client = new MongoClient(uri, options)
    globalWithMongo._mongoClientPromise = client.connect()
  }
  clientPromise = globalWithMongo._mongoClientPromise
} else {
  // In production mode, it's best to not use a global variable.
  client = new MongoClient(uri, options)
  clientPromise = client.connect()
}

/**
 * Get MongoDB database instance
 * @param dbName - Database name (default: 'exceldashboard')
 * @returns Database instance
 */
export async function getDatabase(dbName: string = 'exceldashboard'): Promise<Db> {
  const client = await clientPromise
  return client.db(dbName)
}

/**
 * Get a specific collection from the database
 * @param collectionName - Name of the collection
 * @param dbName - Database name (default: 'exceldashboard')
 * @returns Collection instance
 */
export async function getCollection<T = any>(
  collectionName: string,
  dbName: string = 'exceldashboard'
): Promise<Collection<T>> {
  const db = await getDatabase(dbName)
  return db.collection<T>(collectionName)
}

/**
 * Save Excel data to MongoDB
 * @param data - Parsed Excel data
 * @param filename - Original filename
 * @param uploadedBy - User identifier (optional)
 * @returns Saved document with ID
 */
export async function saveExcelData(
  data: any,
  filename: string,
  uploadedBy?: string
) {
  const collection = await getCollection('excel_uploads')
  
  const document = {
    filename,
    data,
    uploadedBy,
    uploadedAt: new Date(),
    metadata: {
      sheets: Object.keys(data),
      totalRecords: Object.values(data).reduce(
        (total: number, sheet: any) => total + (Array.isArray(sheet) ? sheet.length : 0),
        0
      )
    }
  }
  
  const result = await collection.insertOne(document)
  return { ...document, _id: result.insertedId }
}

/**
 * Get all uploaded Excel files
 * @param limit - Maximum number of records to return
 * @param skip - Number of records to skip
 * @returns Array of uploaded files
 */
export async function getUploadedFiles(limit: number = 10, skip: number = 0) {
  const collection = await getCollection('excel_uploads')
  
  return await collection
    .find({})
    .sort({ uploadedAt: -1 })
    .limit(limit)
    .skip(skip)
    .toArray()
}

/**
 * Get a specific uploaded file by ID
 * @param id - Document ID
 * @returns Uploaded file document
 */
export async function getUploadedFileById(id: string) {
  const collection = await getCollection('excel_uploads')
  const { ObjectId } = require('mongodb')
  
  return await collection.findOne({ _id: new ObjectId(id) })
}

/**
 * Delete an uploaded file by ID
 * @param id - Document ID
 * @returns Deletion result
 */
export async function deleteUploadedFile(id: string) {
  const collection = await getCollection('excel_uploads')
  const { ObjectId } = require('mongodb')
  
  return await collection.deleteOne({ _id: new ObjectId(id) })
}

export default clientPromise