import { NextRequest, NextResponse } from 'next/server'
import { deleteUploadedFile } from '@/lib/mongodb'

/**
 * Delete uploaded Excel data from MongoDB
 * DELETE /api/delete-data
 */
export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json()

    if (!id) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    console.log('Deleting document with ID:', id)

    // Delete document from MongoDB
    const result = await deleteUploadedFile(id)

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Document not found or already deleted' },
        { status: 404 }
      )
    }

    console.log('Document deleted successfully')

    return NextResponse.json({
      success: true,
      message: 'Document deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting data from MongoDB:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    
    return NextResponse.json(
      {
        error: 'Failed to delete data',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}