import { NextRequest, NextResponse } from 'next/server'
import { saveExcelData } from '@/lib/mongodb'

/**
 * Save Excel data to MongoDB
 * POST /api/save-data
 */
export async function POST(request: NextRequest) {
  try {
    const { data, filename, uploadedBy } = await request.json()

    if (!data) {
      return NextResponse.json(
        { error: 'No data provided' },
        { status: 400 }
      )
    }

    if (!filename) {
      return NextResponse.json(
        { error: 'Filename is required' },
        { status: 400 }
      )
    }

    console.log('Saving Excel data to MongoDB:', {
      filename,
      sheets: Object.keys(data),
      uploadedBy: uploadedBy || 'anonymous'
    })

    // Save data to MongoDB
    const savedDocument = await saveExcelData(data, filename, uploadedBy)

    console.log('Data saved successfully with ID:', savedDocument._id)

    return NextResponse.json({
      success: true,
      id: savedDocument._id,
      message: 'Data saved successfully',
      metadata: savedDocument.metadata
    })
  } catch (error) {
    console.error('Error saving data to MongoDB:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    
    return NextResponse.json(
      {
        error: 'Failed to save data',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}