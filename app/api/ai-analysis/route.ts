import { GoogleGenerativeA<PERSON> } from '@google/generative-ai'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { prompt, companyCode, companyData } = await request.json()

    console.log("AI Analysis request received:", { companyCode, hasCompanyData: !!companyData })

    if (!process.env.GEMINI_API_KEY) {
      console.error("Gemini API key not configured")
      return NextResponse.json({ error: "Gemini API key not configured" }, { status: 500 })
    }

    const companyName = companyData?.database?.["Company Name"] || companyCode || "Unknown Company"

    // Send complete company data to Gemini API for comprehensive analysis
    const dataString = JSON.stringify(companyData, null, 2)

    const fullPrompt = `Analyze ${companyName} (${companyCode}) based on: ${prompt}

Company Data:
${dataString}

Please provide a comprehensive analysis focusing on key insights, financial health, and investment perspective. 

IMPORTANT: Format your response in clean, well-structured Markdown with:
- Use **bold** for important metrics and headings
- Use *italics* for emphasis
- Use bullet points and numbered lists for clarity
- Use proper headings (##, ###) to organize sections
- Include line breaks for readability
- Make it visually appealing and easy to read

Keep the response concise but well-formatted.`

    // Print the entire prompt being sent to Gemini API
    console.log("=== FULL PROMPT SENT TO GEMINI API ===")
    console.log(fullPrompt)
    console.log("=== END OF PROMPT ===")

    console.log("Sending request to Gemini API...")

    // Create AbortController for timeout handling
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 60000) // 60 second timeout

    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${process.env.GEMINI_API_KEY}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          signal: controller.signal,
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    text: fullPrompt,
                  },
                ],
              },
            ],
            generationConfig: {
              temperature: 0.7,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 16384,
            },
          }),
        },
      )

      clearTimeout(timeoutId)

      console.log("Gemini API response status:", response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error("Gemini API error:", errorText)
        throw new Error(`Gemini API error: ${response.status} - ${errorText}`)
      }

      const result = await response.json()
      console.log("Gemini API response received successfully")

      const analysis = result.candidates?.[0]?.content?.parts?.[0]?.text || "No analysis generated"

      return NextResponse.json({ analysis })
    } catch (fetchError) {
      clearTimeout(timeoutId)

      if (fetchError.name === 'AbortError') {
        console.error("Request timed out after 60 seconds")
        throw new Error("Request timed out. The AI service is taking longer than expected to respond.")
      }

      throw fetchError
    }
  } catch (error) {
    console.error("AI Analysis error:", error)

    // Provide user-friendly error messages
    let userFriendlyMessage = "We're having trouble generating your analysis right now."
    let actionableAdvice = "Please try again in a few moments."
    
    if (error instanceof Error) {
      const errorMessage = error.message.toLowerCase()
      
      if (errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
        userFriendlyMessage = "Our AI service is currently experiencing high demand."
        actionableAdvice = "Please wait 30 seconds and try again."
      } else if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
        userFriendlyMessage = "Connection to our AI service timed out."
        actionableAdvice = "Please check your internet connection and try again."
      } else if (errorMessage.includes('unauthorized') || errorMessage.includes('api key')) {
        userFriendlyMessage = "There's an issue with our AI service configuration."
        actionableAdvice = "Please contact support if this problem persists."
      } else if (errorMessage.includes('invalid') || errorMessage.includes('bad request')) {
        userFriendlyMessage = "The analysis request couldn't be processed."
        actionableAdvice = "Please try selecting different analysis options."
      }
    }

    return NextResponse.json(
      {
        error: userFriendlyMessage,
        action: actionableAdvice,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}
