import { GoogleGenerativeAI } from '@google/generative-ai'
import { NextRequest } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { prompt, companyCode, companyData } = await request.json()

    console.log("AI Analysis streaming request received:", { companyCode, hasCompanyData: !!companyData })

    if (!process.env.GEMINI_API_KEY) {
      console.error("Gemini API key not configured")
      return new Response(
        JSON.stringify({ error: "Gemini API key not configured" }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const companyName = companyData?.database?.["Company Name"] || companyCode || "Unknown Company"

    // Send complete company data to Gemini API for comprehensive analysis
    const dataString = JSON.stringify(companyData, null, 2)

    const fullPrompt = `Analyze ${companyName} (${companyCode}) based on: ${prompt}

Company Data:
${dataString}

Please provide a comprehensive analysis focusing on key insights, financial health, and investment perspective. 

IMPORTANT: Format your response in clean, well-structured Markdown with:
- Use **bold** for important metrics and headings
- Use *italics* for emphasis
- Use bullet points and numbered lists for clarity
- Use proper headings (##, ###) to organize sections
- Include line breaks for readability
- Make it visually appealing and easy to read

Keep the response concise but well-formatted.`

    console.log("Sending streaming request to Gemini API...")

    // Initialize Gemini AI
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY)
    const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash-preview-05-20" })

    // Create a readable stream
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Send initial status
          controller.enqueue(
            new TextEncoder().encode(
              `data: ${JSON.stringify({ type: 'status', message: 'Connecting to AI service...' })}\n\n`
            )
          )

          // Generate content with streaming
          const result = await model.generateContentStream({
            contents: [{
              parts: [{ text: fullPrompt }]
            }],
            generationConfig: {
              temperature: 0.7,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 16384,
            },
          })

          controller.enqueue(
            new TextEncoder().encode(
              `data: ${JSON.stringify({ type: 'status', message: 'Generating analysis...' })}\n\n`
            )
          )

          let fullText = ''
          
          // Stream the response
          for await (const chunk of result.stream) {
            const chunkText = chunk.text()
            fullText += chunkText
            
            // Send chunk to client
            controller.enqueue(
              new TextEncoder().encode(
                `data: ${JSON.stringify({ type: 'chunk', content: chunkText })}\n\n`
              )
            )
          }

          // Send completion signal
          controller.enqueue(
            new TextEncoder().encode(
              `data: ${JSON.stringify({ type: 'complete', fullContent: fullText })}\n\n`
            )
          )

          controller.close()
        } catch (error) {
          console.error('Streaming error:', error)
          
          let userFriendlyMessage = "We're having trouble generating your analysis right now."
          let actionableAdvice = "Please try again in a few moments."
          
          if (error instanceof Error) {
            const errorMessage = error.message.toLowerCase()
            
            if (errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
              userFriendlyMessage = "Our AI service is currently experiencing high demand."
              actionableAdvice = "Please wait 30 seconds and try again."
            } else if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
              userFriendlyMessage = "Connection to our AI service timed out."
              actionableAdvice = "Please check your internet connection and try again."
            } else if (errorMessage.includes('unauthorized') || errorMessage.includes('api key')) {
              userFriendlyMessage = "There's an issue with our AI service configuration."
              actionableAdvice = "Please contact support if this problem persists."
            }
          }

          controller.enqueue(
            new TextEncoder().encode(
              `data: ${JSON.stringify({ 
                type: 'error', 
                error: userFriendlyMessage,
                action: actionableAdvice,
                timestamp: new Date().toISOString()
              })}\n\n`
            )
          )
          
          controller.close()
        }
      },
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    })
  } catch (error) {
    console.error('AI Analysis streaming setup error:', error)
    return new Response(
      JSON.stringify({ 
        error: "Failed to initialize streaming analysis",
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    )
  }
}