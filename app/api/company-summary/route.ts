import { NextRequest, NextResponse } from "next/server"
import { GoogleGenerativeAI } from "@google/generative-ai"

export async function POST(request: NextRequest) {
  try {
    const { prompt, companyName, companyData } = await request.json()

    console.log("Company summary request received:", { companyName, hasCompanyData: !!companyData })

    if (!process.env.GEMINI_API_KEY) {
      console.error("Gemini API key not configured")
      return NextResponse.json({ error: "Gemini API key not configured" }, { status: 500 })
    }

    // Extract key information for the summary
    const sector = companyData?.database?.["Sector"] || "N/A"
    const marketCapCategory = companyData?.database?.["Market Cap Category"] || "N/A"
    const currentPrice = companyData?.dailyPrice?.["Current Price"] || companyData?.marketCap?.["Current Price"] || "N/A"
    const marketCap = companyData?.marketCap?.["Market Cap"] || "N/A"

    // Create a focused data summary for the AI
    const companyInfo = {
      name: companyName,
      sector: sector,
      marketCapCategory: marketCapCategory,
      currentPrice: currentPrice,
      marketCap: marketCap,
      // Add a few more key fields if available
      ...(companyData?.database?.["Sub Sector"] && { subSector: companyData.database["Sub Sector"] }),
      ...(companyData?.database?.["Industry"] && { industry: companyData.database["Industry"] }),
    }

    const fullPrompt = `${prompt}

Company: ${companyName}
Key Information: ${JSON.stringify(companyInfo, null, 2)}

Requirements:
- Exactly 30-40 words
- Professional tone
- Focus on business nature and sector
- No marketing language
- Be factual and concise
- Do not include financial figures unless essential
- Start with the company's primary business activity`

    console.log("Sending request to Gemini API for company summary...")

    // Initialize Gemini AI
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY)
    const model = genAI.getGenerativeModel({
      model: "gemini-2.5-flash-preview-05-20",
      generationConfig: {
        temperature: 0.7,
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 100, // Limit output for short summaries
      }
    })

    const result = await model.generateContent(fullPrompt)
    const response = await result.response
    const summary = response.text()

    console.log("Received summary from Gemini API:", summary?.substring(0, 100) + "...")

    if (!summary || summary.trim() === "" || summary.trim().length < 10) {
      console.warn("Empty or too short response from Gemini API, using fallback")
      // Return a fallback summary instead of throwing an error
      const fallbackSummary = `${companyName} operates in the ${sector !== 'N/A' ? sector.toLowerCase() : 'financial'} sector, providing specialized services and solutions to its customers and stakeholders.`
      return NextResponse.json({
        summary: fallbackSummary,
        companyName,
        wordCount: fallbackSummary.split(' ').length,
        fallback: true
      })
    }

    // Clean up the summary (remove quotes, extra whitespace, etc.)
    const cleanedSummary = summary.trim()
      .replace(/^["']|["']$/g, '') // Remove surrounding quotes
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()

    return NextResponse.json({
      summary: cleanedSummary,
      companyName,
      wordCount: cleanedSummary.split(' ').length
    })

  } catch (error: any) {
    console.error("Error in company summary API:", error)
    
    return NextResponse.json(
      { 
        error: "Failed to generate company summary",
        details: error.message || "Unknown error"
      },
      { status: 500 }
    )
  }
}
