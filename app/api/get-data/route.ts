import { NextRequest, NextResponse } from 'next/server'
import { getUploadedFiles, getUploadedFileById } from '@/lib/mongodb'

export const dynamic = 'force-dynamic';

/**
 * Get uploaded Excel data from MongoDB
 * GET /api/get-data?id=<document_id> - Get specific document
 * GET /api/get-data?limit=10&skip=0 - Get list of documents
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = parseInt(searchParams.get('skip') || '0')

    if (id) {
      // Get specific document by ID
      console.log('Fetching document by ID:', id)
      
      const document = await getUploadedFileById(id)
      
      if (!document) {
        return NextResponse.json(
          { error: 'Document not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        document
      })
    } else {
      // Get list of documents
      console.log('Fetching documents list:', { limit, skip })
      
      const documents = await getUploadedFiles(limit, skip)
      
      return NextResponse.json({
        success: true,
        documents,
        count: documents.length
      })
    }
  } catch (error) {
    console.error('Error fetching data from MongoDB:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    
    return NextResponse.json(
      {
        error: 'Failed to fetch data',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}