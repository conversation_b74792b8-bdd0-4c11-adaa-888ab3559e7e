"use client"

import { useState, useEffect, useRef } from "react"
import { CardDescription } from "@/components/ui/card"
import { Loader2, Sparkles } from "lucide-react"

interface CompanySummaryProps {
  companyData: {
    database?: any
    dailyPrice?: any
    marketCap?: any
  }
  companyName: string
}

export function CompanySummary({ companyData, companyName }: CompanySummaryProps) {
  const [summary, setSummary] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  useEffect(() => {
    const generateSummary = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Cancel any previous request
        if (abortControllerRef.current) {
          abortControllerRef.current.abort()
        }

        // Create new abort controller
        abortControllerRef.current = new AbortController()

        // Add a small delay to prevent too many rapid API calls
        await new Promise(resolve => setTimeout(resolve, 500))

        // Create a focused prompt for a short company summary
        const prompt = `Generate a concise company summary in exactly 30-40 words. Focus on the company's primary business, sector, and key characteristics. Be professional and informative.`

        const response = await fetch("/api/company-summary", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            prompt,
            companyName,
            companyData,
          }),
          signal: abortControllerRef.current.signal,
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.details || `HTTP error! status: ${response.status}`)
        }

        const { summary: generatedSummary } = await response.json()

        if (!generatedSummary || generatedSummary.trim() === "") {
          throw new Error("Empty summary received")
        }

        setSummary(generatedSummary.trim())
      } catch (error: any) {
        // Don't show error if request was aborted
        if (error.name === 'AbortError') {
          return
        }
        console.error("Error generating company summary:", error)
        setError("Unable to generate summary")
        // Fallback to default text
        setSummary("Essential company identification and classification")
      } finally {
        setIsLoading(false)
      }
    }

    // Only generate summary if we have company data
    if (companyData && (companyData.database || companyData.dailyPrice || companyData.marketCap)) {
      // Use a timeout to ensure this doesn't block the UI
      const timeoutId = setTimeout(generateSummary, 100)
      return () => {
        clearTimeout(timeoutId)
        // Cleanup abort controller on unmount
        if (abortControllerRef.current) {
          abortControllerRef.current.abort()
        }
      }
    } else {
      setSummary("Essential company identification and classification")
      setIsLoading(false)
    }
  }, [companyData, companyName])

  if (isLoading) {
    return (
      <CardDescription className="text-base dark:text-muted-foreground flex items-center gap-2">
        <Loader2 className="h-4 w-4 animate-spin text-primary" />
        <span className="animate-pulse">Generating AI summary...</span>
      </CardDescription>
    )
  }

  if (error) {
    return (
      <CardDescription className="text-base dark:text-muted-foreground">
        Essential company identification and classification
      </CardDescription>
    )
  }

  return (
    <CardDescription className="text-base dark:text-muted-foreground flex items-start gap-2">
      <Sparkles className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
      <span>{summary}</span>
    </CardDescription>
  )
}
